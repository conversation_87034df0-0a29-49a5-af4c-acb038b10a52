import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Button,
  Empty,
  Spin
} from 'antd';
import {
  UpOutlined,
  DownOutlined,
  LineChartOutlined,
  SettingOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import PLCDataExplorerConfigDrawer from './PLCDataExplorerConfigDrawer';
import dayjs from 'dayjs';
import './styles/PLCStyles.css';

interface PLCDataExplorerProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onDataLoad?: (data: any) => void;
  onSelectionExplore?: (selection: { batchIds: string[]; timeRange: { startTime: number; endTime: number } }) => void;
  onFiltersChange?: (filters: any) => void;
  onSidebarCollapse?: () => void;
}

const PLCDataExplorer: React.FC<PLCDataExplorerProps> = ({
  isCollapsed,
  onToggleCollapse,
  onDataLoad,
  onSelectionExplore,
  onFiltersChange,
  onSidebarCollapse
}) => {
  const [configDrawerOpen, setConfigDrawerOpen] = useState(false);
  const [explorerData, setExplorerData] = useState<any>(null);
  const [configurationLoading, setConfigurationLoading] = useState(false);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);
  // Change selection state type to always have non-null timeRange when set
  const [selection, setSelection] = useState<{
    selections: Array<{
      batchIds: string[];
      timeRange: { startTime: number; endTime: number };
    }>;
  } | null>(null);

  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);

  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
        setIsChartReady(offsetWidth > 0 && offsetHeight > 0);
      }
    };

    updateSize();

    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [isCollapsed]);

  const handleConfigDataLoad = (data: any) => {
    setExplorerData(data);
    setConfigurationLoading(false);

    if (onDataLoad) {
      onDataLoad(data);
    }

    setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 100);
  };

  const handleOpenConfig = () => {
    setConfigDrawerOpen(true);
    setConfigurationLoading(true);
  };

  const handleCloseConfig = () => {
    setConfigDrawerOpen(false);
    setConfigurationLoading(false);
  };

  useEffect(() => {
    if (echartsRef.current) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        chartInstance.dispose();
      }
    }

    const timeoutId = setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [explorerData?.selectedBatches?.join(',')]);

  // Fix for chart not resizing properly after collapse/expand
  useEffect(() => {
    if (echartsRef.current && isChartReady && containerSize.width > 0 && containerSize.height > 0) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        // Small delay to ensure DOM has fully updated
        const timeoutId = setTimeout(() => {
          chartInstance.resize();
        }, 100);
        return () => clearTimeout(timeoutId);
      }
    }
  }, [containerSize.width, containerSize.height, isChartReady, isCollapsed]);

  const plotData = useMemo(() => {
    if (!explorerData || !explorerData.columnOptions) {
      return [];
    }

    const columnOptions = explorerData.columnOptions;
    const qualityLookup = explorerData.quality_data || {};

    const result: any[] = [];

    // Handle direct series array in columnOptions
    if (columnOptions.series && Array.isArray(columnOptions.series)) {
      // Get the actual column name from metadata.y_axis in the API response
      const actualColumnName = explorerData.metadata?.y_axis || 'Value';

      columnOptions.series.forEach((series: any, seriesIndex: number) => {

        if (series && series.data && series.data.length > 0) {
          const batchId = series.name || `Series_${seriesIndex + 1}`;
          const qualityInfo = qualityLookup[batchId] || {};

          const timestamps = series.data.map((point: any) => new Date(point[0]).getTime());
          const startTime = Math.min(...timestamps);
          const endTime = Math.max(...timestamps);

          result.push({
            name: series.name || `Series ${seriesIndex + 1}`,
            data: series.data,
            groupValue: series.name || null,
            originalColumn: actualColumnName,
            lineStyle: series.lineStyle || { width: 2 },
            symbolSize: series.symbolSize || 4,
            color: series.color || series.lineStyle?.color || null,
            seriesIndex: seriesIndex,
            columnName: actualColumnName,
            batchId: batchId,
            batchStartTime: startTime,
            batchEndTime: endTime,
            qualityData: qualityInfo
          });
        }
      });
    } else {
      // Fallback: Handle legacy structure with named columns
      const availableColumns = Object.keys(columnOptions).filter(col => col !== 'DateTime');
      const firstColumn = availableColumns.length > 0 ? availableColumns[0] : null;

      if (firstColumn) {
        const columnName = firstColumn;
        const columnData = columnOptions[columnName];

        if (columnData?.series && Array.isArray(columnData.series)) {
          columnData.series.forEach((series: any, seriesIndex: number) => {
            if (series && series.data && series.data.length > 0) {
              const batchId = series.name || `Batch_${seriesIndex + 1}`;
              const qualityInfo = qualityLookup[batchId] || {};

              const timestamps = series.data.map((point: any) => new Date(point[0]).getTime());
              const startTime = Math.min(...timestamps);
              const endTime = Math.max(...timestamps);

              result.push({
                name: `${columnName} - ${batchId}`,
                data: series.data,
                groupValue: series.name || null,
                originalColumn: columnName,
                lineStyle: series.lineStyle || { width: 2 },
                symbolSize: series.symbolSize || 4,
                color: series.color || series.lineStyle?.color || null,
                seriesIndex: seriesIndex,
                columnName: columnName,
                batchId: batchId,
                batchStartTime: startTime,
                batchEndTime: endTime,
                qualityData: qualityInfo
              });
            }
          });
        }
      }
    }
    return result;
  }, [explorerData]);

  const createChartOption = useMemo(() => {
    if (!plotData || plotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    const firstColumnName = plotData.length > 0 ? plotData[0].columnName : 'Value';

    let min = Infinity;
    let max = -Infinity;

    plotData.forEach((series: any) => {
      if (series.data && Array.isArray(series.data)) {
        series.data.forEach((point: any) => {
          if (point && Array.isArray(point) && typeof point[1] === 'number') {
            min = Math.min(min, point[1]);
            max = Math.max(max, point[1]);
          }
        });
      }
    });

    const yAxisRange = min !== Infinity && max !== -Infinity ? {
      min: min - (max - min) * 0.05,
      max: max + (max - min) * 0.05
    } : null;

    const chartOption = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' },
        appendToBody: true,
        className: 'batch-comparison-tooltip',
        extraCssText: 'z-index: 9999; position: fixed;',
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';
          
          // Group tooltip content by batch
          const batchGroups: {[key: string]: any} = {};
          
          params.forEach((param: any) => {
            const seriesData = plotData.find(p => p.name === param.seriesName);
            
                      if (seriesData) {
            const batchId = seriesData.batchId;
            
            if (!batchGroups[batchId]) {
              batchGroups[batchId] = {
                batchId: batchId,
                startTime: new Date(seriesData.batchStartTime).toLocaleString(),
                endTime: new Date(seriesData.batchEndTime).toLocaleString(),
                qualityData: seriesData.qualityData,
                series: []
              };
            }
              
              batchGroups[batchId].series.push({
                name: param.seriesName,
                value: param.value,
                color: param.color,
                columnName: seriesData.columnName
              });
            }
          });
          
          let tooltipHtml = '';
          Object.values(batchGroups).forEach((batch: any) => {
            // Format quality data for display
            const qualityEntries = Object.entries(batch.qualityData || {});
            const qualityDisplay = qualityEntries.map(([key, value]) => `${key}: ${value}`).join(', ');
            
            tooltipHtml += `
              <div style="
                background: linear-gradient(135deg, #f8f9fa, #ffffff);
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              ">
                <div style="
                  font-weight: 600;
                  font-size: 14px;
                  color: #333;
                  margin-bottom: 8px;
                  display: flex;
                  align-items: center;
                  gap: 6px;
                ">
                  <span style="color: #333;">Batch:</span> ${batch.batchId}
                </div>
               
               <div style="
                 display: grid;
                 grid-template-columns: auto 1fr;
                 gap: 4px 8px;
                 font-size: 12px;
                 color: #666;
                 margin-bottom: 8px;
               ">
                 <span style="font-weight: 500;">⏰ Start:</span>
                 <span>${batch.startTime}</span>
                 <span style="font-weight: 500;">⏰ End:</span>
                 <span>${batch.endTime}</span>
                 <span style="font-weight: 500;">📊 Quality1112:</span>
                 <span style="color: #1890ff; font-weight: 600;">
                   ${qualityDisplay || 'No data'}
                 </span>
               </div>
               
               <div style="
                 border-top: 1px solid #f0f0f0;
                 padding-top: 8px;
                 font-size: 12px;
               ">
                 ${batch.series.map((s: any) => `
                   <div style="
                     display: flex;
                     justify-content: space-between;
                     align-items: center;
                     padding: 2px 0;
                   ">
                     <span style="
                       display: flex;
                       align-items: center;
                       gap: 6px;
                       color: #333;
                       font-weight: 500;
                     ">
                       <span style="
                         display: inline-block;
                         width: 8px;
                         height: 8px;
                         border-radius: 50%;
                         background: ${s.color};
                       "></span>
                       ${s.columnName}
                     </span>
                     <span style="
                       font-weight: 600;
                       color: #1890ff;
                     ">
                       ${Array.isArray(s.value) ? s.value[1]?.toFixed(2) : s.value?.toFixed(2)}
                     </span>
                   </div>
                 `).join('')}
               </div>
             </div>
           `;
          });
          
          return tooltipHtml;
        }
      },
      brush: {
        toolbox: ['lineX', 'clear'],
        xAxisIndex: 0,
        brushType: 'lineX',
        brushMode: 'multiple',
        brushStyle: {
          borderWidth: 2,
          color: 'rgba(24, 144, 255, 0.1)',
          borderColor: 'rgba(24, 144, 255, 0.8)'
        },
        z: 10000
      },
      toolbox: {
        show: true,
        feature: {
          brush: {
            type: ['lineX', 'keep', 'clear']
          },
        },
        right: 20,
        top: 20
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0],
          yAxisIndex: [], // Only horizontal zoom
          filterMode: 'filter'
        }
      ],
      grid: {
        left: '60px',
        right: '40px',
        top: '8%',
        bottom: 20,
        containLabel: true
      },
      xAxis: {
        type: 'time',
        name: 'DateTime',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1,
            type: 'solid'
          }
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#666' }
        },
        axisTick: { show: true },
        axisLabel: {
          show: true,
          fontSize: 11,
          color: '#666'
        }
      },
      yAxis: {
        type: 'value',
        name: firstColumnName,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        scale: false,
        silent: true, // Disable Y-axis interaction
        min: yAxisRange?.min,
        max: yAxisRange?.max,
        axisLabel: {
          formatter: function (value: any) {
            return typeof value === 'number' ? value.toFixed(2) : value;
          }
        }
      },
      series: plotData.map((s) => ({
        name: s.name,
        type: 'line',
        data: s.data || [],
        smooth: false,
        symbol: 'circle',
        symbolSize: s.symbolSize || 4,
        lineStyle: {
          width: s.lineStyle?.width || 2,
          color: s.color || s.lineStyle?.color || '#1f77b4'
        },
        itemStyle: {
          color: s.color || s.lineStyle?.color || '#1f77b4'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)'
          }
        }
      })),
      hasData: true
    };

    return chartOption;
  }, [plotData]);

  return (
    <div className={`plc-data-explorer ${isCollapsed ? 'collapsed' : 'expanded'}`}>
      {/* Header Section - Always Visible */}
      <div 
        style={{
          background: '#ffffff',
          borderTop: '1px solid #e8e8e8',
          borderLeft: '1px solid #e8e8e8',
          borderRight: '1px solid #e8e8e8',
          borderRadius: '8px 8px 0 0',
          padding: '12px 16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 -2px 8px rgba(0,0,0,0.06)',
          position: 'relative',
          zIndex: 10
        }}
      >
        {/* Left Side - Title */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '16px',
          fontWeight: 600,
          color: '#333',
          cursor: 'pointer'
        }} onClick={onToggleCollapse}>
          <LineChartOutlined style={{ color: '#1890ff' }} />
          Batch Overview
          {isCollapsed ? <UpOutlined style={{ fontSize: '12px', color: '#999' }} /> : <DownOutlined style={{ fontSize: '12px', color: '#999' }} />}
        </div>

        {/* Right Side - Date Range & Config Button */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {explorerData?.dateRange && (
            <span style={{ 
              fontSize: '13px', 
              color: '#1890ff',
              fontWeight: 500,
              background: '#f0f8ff',
              padding: '4px 8px',
              borderRadius: '4px',
              border: '1px solid #91d5ff'
            }}>
              📅 {dayjs(explorerData.dateRange.startDateTime).format('MMM DD')} - {dayjs(explorerData.dateRange.endDateTime).format('MMM DD, YYYY')}
            </span>
          )}
          {explorerData && (
            <span style={{ 
              fontSize: '12px', 
              color: '#666',
              marginRight: '4px'
            }}>
              {plotData.length} batches
            </span>
          )}
          <Button
            icon={<SettingOutlined />}
            onClick={handleOpenConfig}
            size="small"
            style={{
              borderRadius: '6px',
              border: '1px solid #d9d9d9',
              background: '#fff',
              color: '#666',
              transition: 'none'
            }}
          >
            Configure
          </Button>
          <Button
            type="primary"
            icon={<LineChartOutlined />}
            onClick={() => {
              if (selection && selection.selections.length > 0 && onSelectionExplore) {
                // Collect all unique batch IDs from all selections
                const allBatchIds = Array.from(new Set(
                  selection.selections.flatMap(sel => sel.batchIds)
                ));
                
                // Use the combined time range of all selections
                const allStartTimes = selection.selections.map(sel => sel.timeRange.startTime);
                const allEndTimes = selection.selections.map(sel => sel.timeRange.endTime);
                const combinedTimeRange = {
                  startTime: Math.min(...allStartTimes),
                  endTime: Math.max(...allEndTimes)
                };

                if (onSidebarCollapse) {
                  onSidebarCollapse();
                }
                
                onSelectionExplore({
                  batchIds: allBatchIds,
                  timeRange: combinedTimeRange
                });
              }
            }}
            size="small"
            style={{ borderRadius: '6px', boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)' }}
            disabled={!selection || selection.selections.length === 0}
          >
            Explore Selected Batches ({selection?.selections.length || 0})
          </Button>
        </div>
      </div>

      {/* Explorer Content - Collapsible */}
      {!isCollapsed && (
        <div 
          className="explorer-content" 
          style={{ 
            background: 'white',
            border: '1px solid #e8e8e8',
            borderTop: 'none',
            height: 'calc(100% - 60px)',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Chart Section */}
          <div 
            ref={containerRef}
            style={{ 
              flex: 1, 
              padding: '16px',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {configurationLoading ? (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                flex: 1 
              }}>
                <Spin size="large" tip="Configuring explorer data..." />
              </div>
            ) : !explorerData ? (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                flex: 1 
              }}>
                <Empty
                  image={<LineChartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
                  description={
                    <div>
                      <h4 style={{ margin: '8px 0', color: '#666' }}>No Data Loaded</h4>
                      <p style={{ margin: 0, color: '#999', fontSize: '12px' }}>
                        Click "Configure" to load data and view batch comparison charts
                      </p>
                    </div>
                  }
                />
              </div>
            ) : (
              <div className="h-full w-full" style={{ flex: 1 }}>
                {containerSize.width > 0 && isChartReady && (
                  <ReactECharts
                    key={`explorer-chart-${chartUpdateTrigger}`}
                    ref={echartsRef}
                    option={createChartOption}
                    style={{ width: '100%', height: '100%' }}
                    opts={{ renderer: 'canvas' }}
                    notMerge={true}
                    onChartReady={(chartInstance: any) => {
                      const handleBrushSelected = (params: any) => {
                        if (params.batch && params.batch.length > 0) {
                          const brushComponent = params.batch[0];

                          if (brushComponent.areas && brushComponent.areas.length > 0) {
                            // Process all current brush areas (multiple selections)
                            const newSelections: Array<{
                              batchIds: string[];
                              timeRange: { startTime: number; endTime: number };
                            }> = [];

                            brushComponent.areas.forEach((area: any) => {
                              const coordRange = area.coordRange || area.coordRanges?.[0];

                              if (coordRange && coordRange.length === 2) {
                                const [startTime, endTime] = coordRange;
                                const selectedBatchIds: string[] = [];
                                const selectedSeries: {[key: number]: number[]} = {};
                                let hasValidSelection = false;

                                plotData.forEach((seriesData, seriesIndex) => {
                                  if (seriesData.data && Array.isArray(seriesData.data)) {
                                    const selectedIndices: number[] = [];

                                    seriesData.data.forEach((point: any, pointIndex: number) => {
                                      if (point && Array.isArray(point) && point.length >= 2) {
                                        const pointTime = new Date(point[0]).getTime();

                                        if (pointTime >= startTime && pointTime <= endTime) {
                                          selectedIndices.push(pointIndex);
                                        }
                                      }
                                    });

                                    if (selectedIndices.length > 0) {
                                      selectedSeries[seriesIndex] = selectedIndices;
                                      hasValidSelection = true;

                                      if (seriesData.batchId && !selectedBatchIds.includes(seriesData.batchId)) {
                                        selectedBatchIds.push(seriesData.batchId);
                                      }
                                    }
                                  }
                                });

                                if (hasValidSelection && startTime && endTime) {
                                  const uniqueBatchIds = Array.from(new Set(selectedBatchIds));
                                  newSelections.push({
                                    batchIds: uniqueBatchIds,
                                    timeRange: { startTime, endTime }
                                  });
                                }
                              }
                            });

                            // Update selection state with all current selections
                            if (newSelections.length > 0) {
                              setSelection({ selections: newSelections });

                              // Apply visual feedback for multiple selections
                              const currentOption = chartInstance.getOption();
                              const updatedSeries = currentOption.series.map((series: any, index: number) => {
                                // Check if this series has any selected data points across all selections
                                let hasAnySelection = false;
                                const allSelectedIndices: number[] = [];

                                newSelections.forEach(selection => {
                                  plotData.forEach((seriesData, seriesIndex) => {
                                    if (seriesIndex === index && seriesData.data && Array.isArray(seriesData.data)) {
                                      const selectedIndices: number[] = [];
                                      
                                      seriesData.data.forEach((point: any, pointIndex: number) => {
                                        if (point && Array.isArray(point) && point.length >= 2) {
                                          const pointTime = new Date(point[0]).getTime();
                                          
                                          if (pointTime >= selection.timeRange.startTime && pointTime <= selection.timeRange.endTime) {
                                            selectedIndices.push(pointIndex);
                                            allSelectedIndices.push(pointIndex);
                                          }
                                        }
                                      });
                                      
                                      if (selectedIndices.length > 0) {
                                        hasAnySelection = true;
                                      }
                                    }
                                  });
                                });

                                if (hasAnySelection) {
                                  const originalColor = plotData[index]?.color || series.lineStyle?.color;
                                  
                                  const newData = series.data.map((point: any, pointIndex: number) => {
                                    const isSelected = allSelectedIndices.includes(pointIndex);

                                    if (Array.isArray(point)) {
                                      return {
                                        value: point,
                                        itemStyle: {
                                          color: isSelected ? originalColor : '#cccccc'
                                        }
                                      };
                                    } else {
                                      return {
                                        ...point,
                                        itemStyle: {
                                          color: isSelected ? originalColor : '#cccccc'
                                        }
                                      };
                                    }
                                  });

                                  return {
                                    ...series,
                                    data: newData,
                                    lineStyle: {
                                      ...series.lineStyle,
                                      color: '#cccccc'
                                    }
                                  };
                                } else {
                                  return {
                                    ...series,
                                    lineStyle: {
                                      ...series.lineStyle,
                                      color: '#cccccc'
                                    },
                                    itemStyle: {
                                      ...series.itemStyle,
                                      color: '#cccccc'
                                    }
                                  };
                                }
                              });

                              chartInstance.setOption({ series: updatedSeries }, false);
                            }
                          }
                        }
                      };

                      const handleBrush = (params: any) => {
                        if (!params.areas || params.areas.length === 0) {
                          // Clear all selections
                          setSelection(null);
                          
                          // Restore original colors
                          const currentOption = chartInstance.getOption();
                          const clearedSeries = currentOption.series.map((series: any, index: number) => ({
                            ...series,
                            data: plotData[index]?.data || series.data,
                            lineStyle: {
                              ...series.lineStyle,
                              color: plotData[index]?.color || series.lineStyle?.color
                            },
                            itemStyle: {
                              ...series.itemStyle,
                              color: plotData[index]?.color || series.itemStyle?.color
                            }
                          }));

                          chartInstance.setOption({ series: clearedSeries }, false);
                        }
                      };

                      chartInstance.on('brushSelected', handleBrushSelected);
                      chartInstance.on('brush', handleBrush);
                    }}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Configuration Drawer */}
      <PLCDataExplorerConfigDrawer
        open={configDrawerOpen}
        onClose={handleCloseConfig}
        onDataLoad={handleConfigDataLoad}
        onFiltersChange={onFiltersChange}
      />
    </div>
  );
};

export default PLCDataExplorer; 