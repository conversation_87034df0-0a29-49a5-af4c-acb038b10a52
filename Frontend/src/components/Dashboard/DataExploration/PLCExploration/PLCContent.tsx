import React, { useState, useRef, useCallback } from 'react';
import {
  PLCContentProps,
  PLCGridItemData,
  PLCLayout,
  PLCComponentType,
  PLCPanelConfiguration,
  DEFAULT_PLC_PANEL_CONFIG,
  PLC_PANEL_SIZES
} from './types/PLCTypes';
import PLCGridLayout from './PLCGridLayout';
import PLCPanelConfigDrawer from './PLCPanelConfigDrawer';
import PhaseIdentificationConfigDrawer from './PhaseIdentificationConfigDrawer';
import PLCDataExplorer from './PLCDataExplorer';
import './styles/PLCStyles.css';
import { postRequest } from '../../../../utils/apiHandler';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';
import { formatQuery } from 'react-querybuilder';

const PLCContent: React.FC<PLCContentProps> = ({
  selectedDataSource, // Will be used in future tasks
  onDataSourceChange, // Will be used in future tasks
  activePanels = [],
  onPanelsChange,
  onSidebarCollapse
}) => {
  // State management
  const [items, setItems] = useState<PLCGridItemData[]>([]);
  const [layout, setLayout] = useState<PLCLayout>([]);
  const [nextId, setNextId] = useState(1);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedPanelId, setSelectedPanelId] = useState<string | null>(null);
  const [panelConfigurations, setPanelConfigurations] = useState<Record<string, PLCPanelConfiguration>>({});
  const [currentlyDisplayedColumns, setCurrentlyDisplayedColumns] = useState<string[]>([]);
  
  // PLCDataExplorer state management
  const [explorerCollapsed, setExplorerCollapsed] = useState(false);
  const [explorerData, setExplorerData] = useState<any>(null);

  // Phase comparison loading state for context menu operations
  const [phaseComparisonLoading, setPhaseComparisonLoading] = useState(false);

  // Individual panel loading states for "Explore Selected Batches" functionality
  const [batchComparisonLoading, setBatchComparisonLoading] = useState(false);
  const [phaseIdentificationLoading, setPhaseIdentificationLoading] = useState(false);

  // Error state management
  const [panelErrors, setPanelErrors] = useState<Record<string, { hasError: boolean; errorMessage: string }>>({});

  const [appliedFilters, setAppliedFilters] = useState<any>(null);

  // Quality data state for sharing between panels
  const [qualityLookup, setQualityLookup] = useState<Record<string, any>>({});

  // Refs
  const gridLayoutRef = useRef<{getLayout: () => PLCLayout, getItems: () => PLCGridItemData[]}>(null);

  const selectSystems = useSelector((state: any) => state.systems.systems);
  const getSystemNames = () => {
    if (!selectSystems || !selectSystems.length || !selectSystems[0]?.systems) {
      return [];
    }
    return selectSystems[0].systems.map((system: any) => ({
      systemId: system.systemId,
      systemName: system.systemName
    }));
  };

  // Helper function to get batch identifier from PLC configuration
  const getBatchIdentifier = () => {
    return selectSystems[0]?.config[0]?.PLC?.batch_identifier || 'batch_id';
  };

  // Helper function to get phase identifier from PLC configuration
  const getPhaseIdentifier = () => {
    return selectSystems[0]?.config[0]?.PLC?.phase_identifier || 'Phase';
  };

  // Handle dropping a panel from sidebar
  const handleDrop = useCallback((componentType: PLCComponentType, x: number, y: number) => {
    
    // Check if this panel type already exists (single panel restriction)
    const existingPanel = items.find(item => item.type === componentType);
    if (existingPanel) {
      return;
    }
    
    const newItem: PLCGridItemData = {
      id: `plc-panel-${nextId}`,
      type: componentType,
      title: componentType === PLCComponentType.BatchComparisonPanel 
        ? 'Batch Comparison' 
        : componentType === PLCComponentType.PhaseIdentificationPanel
        ? 'Phase Comparison'
        : 'Unknown Panel',
      config: DEFAULT_PLC_PANEL_CONFIG,
      isConfigured: false,
    };

    const newLayoutItem = {
      i: newItem.id,
      x: Math.max(0, Math.min(x, 12 - PLC_PANEL_SIZES.DEFAULT.w)),
      y: Math.max(0, y),
      w: PLC_PANEL_SIZES.DEFAULT.w,
      h: PLC_PANEL_SIZES.DEFAULT.h,
      minW: PLC_PANEL_SIZES.MIN.w,
      minH: PLC_PANEL_SIZES.MIN.h,
    };

    const updatedItems = [...items, newItem];
    setItems(updatedItems);
    setLayout(prev => [...prev, newLayoutItem]);
    setNextId(prev => prev + 1);

    // Notify parent about panel changes
    if (onPanelsChange) {
      const newActivePanels = updatedItems.map(item => item.type);
      onPanelsChange(newActivePanels);
    }
  }, [items, nextId, onPanelsChange]);

  // Handle layout changes
  const handleLayoutChange = useCallback((newLayout: PLCLayout) => {
    setLayout(newLayout);
  }, []);

  // Handle items changes
  const handleItemsChange = useCallback((newItems: PLCGridItemData[]) => {
    setItems(newItems);
  }, []);

  // Handle panel removal
  const handlePanelRemove = useCallback((panelId: string) => {
    // Remove from configurations
    setPanelConfigurations(prev => {
      const newConfigs = { ...prev };
      delete newConfigs[panelId];
      return newConfigs;
    });

    // Notify parent about panel changes
    if (onPanelsChange) {
      const updatedItems = items.filter(item => item.id !== panelId);
      const newActivePanels = updatedItems.map(item => item.type);
      onPanelsChange(newActivePanels);
    }

  }, [items, onPanelsChange]);

  // Handle opening configuration drawer
  const handleOpenConfiguration = useCallback((panelId: string, displayedColumns?: string[]) => {
    setSelectedPanelId(panelId);
    setCurrentlyDisplayedColumns(displayedColumns || []);
    setDrawerOpen(true);
  }, []);

  // Handle configuration save
  const handleConfigurationSave = useCallback((panelId: string, config: PLCPanelConfiguration) => {
    // Update panel configurations
    setPanelConfigurations(prev => ({
      ...prev,
      [panelId]: config
    }));

    // Update the panel item to mark it as configured
    const updatedItems = items.map(item =>
      item.id === panelId
        ? { ...item, config, isConfigured: true, title: config.title || item.title }
        : item
    );
    setItems(updatedItems);


  }, [items]);

  // Handle drawer close
  const handleDrawerClose = useCallback(() => {
    setDrawerOpen(false);
    setSelectedPanelId(null);
  }, []);

  // Handle creating phase comparison from batch comparison
  const handleCreatePhaseIdentification = useCallback(async (batchId: string) => {
    // Find the batch comparison panel to get configuration data
    const batchPanel = items.find(item => item.type === PLCComponentType.BatchComparisonPanel);
    if (!batchPanel?.config?.dataRange) {
      console.error('Batch comparison panel not found or not configured');
      return;
    }

    // Set loading state
    setPhaseComparisonLoading(true);

    try {
      const requestPayload = {
        start_datetime: `${batchPanel.config.dataRange.startDate} ${batchPanel.config.dataRange.startTime || '00:00'}:00`,
        end_datetime: `${batchPanel.config.dataRange.endDate} ${batchPanel.config.dataRange.endTime || '23:59'}:59`,
        batch_ids: [batchId],
        system: batchPanel.config.apiData?.system || 'VAT',
        x_axis: 'DateTime',
        y_axis: batchPanel.config.basic?.selectedColumns?.headers?.slice(0, 1) || ['Temperature'],
        phase_identifier: getPhaseIdentifier(),
        batch_identifier: getBatchIdentifier()
      };

      const response = await postRequest('/file/explore-plc/phase-identification', requestPayload);

      if (response.data.data && Object.keys(response.data.data).length > 0) {
        const phaseApiData = response.data.data;
        const availableColumns = batchPanel.config.apiData?.columns || [];

        const phaseConfig = {
          dataRange: batchPanel.config.dataRange,
          basic: {
            xAxisColumn: 'DateTime',
            selectedColumns: {
              indices: [0],
              headers: [phaseApiData.metadata?.y_axis || 'Temperature']
            },
            group: 'batch_id'
          },
          advanced: { windowMode: false },
          apiData: {
            ...phaseApiData,
            selectedBatches: [batchId],
            availableBatches: batchPanel.config.apiData?.availableBatches || [batchId],
            qualityBatchIds: batchPanel.config.apiData?.qualityBatchIds || [{
              batch_id: batchId,
              batch_quality: 8.5,
              is_batch_good: true
            }],
            compareLimit: 1,
            columns: availableColumns,
            batchId: batchId,
            system: batchPanel.config.apiData?.system || 'VAT',
            xAxis: 'DateTime',
            yAxis: phaseApiData.metadata?.y_axis || 'Temperature',
            identifier: getBatchIdentifier(),
            phase_identifier: getPhaseIdentifier()
          },
          title: 'Phase Comparison',
          panelType: PLCComponentType.PhaseIdentificationPanel,
          lastModified: new Date().toISOString()
        };

        // Check if phase comparison panel already exists
        const phasePanel = items.find(item => item.type === PLCComponentType.PhaseIdentificationPanel);
        if (!phasePanel) {
          // Create new phase comparison panel
          const phasePanelId = `plc-panel-${nextId}`;
          setItems(prev => [...prev, {
            id: phasePanelId,
            type: PLCComponentType.PhaseIdentificationPanel,
            title: 'Phase Comparison',
            config: phaseConfig,
            isConfigured: true
          }]);
          setLayout(prev => [...prev, {
            i: phasePanelId,
            x: 6,
            y: 0,
            w: PLC_PANEL_SIZES.DEFAULT.w,
            h: 9,
            minW: PLC_PANEL_SIZES.MIN.w,
            minH: PLC_PANEL_SIZES.MIN.h
          }]);
          setPanelConfigurations(prev => ({ ...prev, [phasePanelId]: phaseConfig }));
          setNextId(prev => prev + 1);
        } else {
          // Update existing phase comparison panel
          setPanelConfigurations(prev => ({ ...prev, [phasePanel.id]: phaseConfig }));
          setItems(prev => prev.map(item =>
            item.id === phasePanel.id
              ? { ...item, config: phaseConfig, isConfigured: true }
              : item
          ));
        }
      } else {
        // No data received or empty data
        const errorMessage = 'No data received from phase comparison API';
        const phasePanel = items.find(item => item.type === PLCComponentType.PhaseIdentificationPanel);

        if (!phasePanel) {
          // Create new error panel
          const phasePanelId = `plc-panel-${nextId}`;
          setItems(prev => [...prev, {
            id: phasePanelId,
            type: PLCComponentType.PhaseIdentificationPanel,
            title: 'Phase Comparison',
            config: undefined,
            isConfigured: true, // Set to true so error UI shows instead of "Not Configured"
            hasError: true,
            errorMessage
          }]);
          setLayout(prev => [...prev, {
            i: phasePanelId,
            x: 6,
            y: 0,
            w: PLC_PANEL_SIZES.DEFAULT.w,
            h: 9,
            minW: PLC_PANEL_SIZES.MIN.w,
            minH: PLC_PANEL_SIZES.MIN.h
          }]);
          setNextId(prev => prev + 1);
        } else {
          // Update existing panel with error
          setItems(prev => prev.map(item =>
            item.id === phasePanel.id
              ? { ...item, config: undefined, isConfigured: true, hasError: true, errorMessage }
              : item
          ));
        }
      }
    } catch (error: any) {
      console.error('Phase Comparison Error:', error);

      // Create/update panel with error state
      const errorMessage = `API Error: ${error?.message || 'Failed to fetch phase comparison data'}`;
      const phasePanel = items.find(item => item.type === PLCComponentType.PhaseIdentificationPanel);

      if (!phasePanel) {
        const phasePanelId = `plc-panel-${nextId}`;
        setItems(prev => [...prev, {
          id: phasePanelId,
          type: PLCComponentType.PhaseIdentificationPanel,
          title: 'Phase Comparison',
          config: undefined,
          isConfigured: true, // Set to true so error UI shows instead of "Not Configured"
          hasError: true,
          errorMessage
        }]);
        setLayout(prev => [...prev, {
          i: phasePanelId,
          x: 6,
          y: 0,
          w: PLC_PANEL_SIZES.DEFAULT.w,
          h: 9,
          minW: PLC_PANEL_SIZES.MIN.w,
          minH: PLC_PANEL_SIZES.MIN.h
        }]);
        setNextId(prev => prev + 1);
      } else {
        setItems(prev => prev.map(item =>
          item.id === phasePanel.id
            ? { ...item, config: undefined, isConfigured: true, hasError: true, errorMessage }
            : item
        ));
      }
    } finally {
      // Reset loading state
      setPhaseComparisonLoading(false);
    }
  }, [items, nextId]);

  // Get current panel configuration
  const getCurrentConfiguration = () => {
    if (!selectedPanelId) return undefined;
    const currentConfig = panelConfigurations[selectedPanelId] || DEFAULT_PLC_PANEL_CONFIG;

    // For phase comparison panels, merge with batch comparison data if available
    if (getCurrentPanelType() === PLCComponentType.PhaseIdentificationPanel) {
      const batchComparisonPanel = items.find(item => item.type === PLCComponentType.BatchComparisonPanel);
      if (batchComparisonPanel && panelConfigurations[batchComparisonPanel.id]) {
        const batchConfig = panelConfigurations[batchComparisonPanel.id];
        return {
          ...currentConfig,
          dataRange: batchConfig.dataRange,
          apiData: {
            ...currentConfig.apiData,
            selectedBatches: batchConfig.apiData?.selectedBatches || [],
            availableBatches: batchConfig.apiData?.availableBatches || [],
            qualityBatchIds: batchConfig.apiData?.qualityBatchIds || [],
            compareLimit: batchConfig.apiData?.compareLimit || 5,
            columns: batchConfig.apiData?.columns || []
          }
        };
      }
    }

    return currentConfig;
  };

  // Get current panel type
  const getCurrentPanelType = () => {
    if (!selectedPanelId) return null;
    const panel = items.find(item => item.id === selectedPanelId);
    return panel?.type || null;
  };

  // Handle PLCDataExplorer toggle collapse
  const handleExplorerToggleCollapse = useCallback(() => {
    // Allow collapse/expand functionality (collapse is always allowed per user requirements)
    setExplorerCollapsed(prev => !prev);
  }, []);

  // Handle PLCDataExplorer data load
  const handleExplorerDataLoad = useCallback((data: any) => {
    setExplorerData(data);

    // Extract and set quality lookup data for sharing between panels
    if (data && data.quality_data) {
      setQualityLookup(data.quality_data);
    } else {
      // Clear quality lookup if no quality data
      setQualityLookup({});
    }
  }, []);

  const handleSelectionExplore = useCallback(async (selection: { batchIds: string[]; timeRange: { startTime: number; endTime: number } }) => {
    // Set individual loading states for each panel
    setBatchComparisonLoading(true);
    setPhaseIdentificationLoading(true);

    // Add panels immediately if they don't exist, so users see loading states
    const batchPanel = items.find(item => item.type === PLCComponentType.BatchComparisonPanel);
    const phasePanel = items.find(item => item.type === PLCComponentType.PhaseIdentificationPanel);

    let batchPanelId: string;
    let phasePanelId: string;

    // Add BatchComparisonPanel if it doesn't exist
    if (!batchPanel) {
      batchPanelId = `plc-panel-${nextId}`;
      setItems(prev => [...prev, {
        id: batchPanelId,
        type: PLCComponentType.BatchComparisonPanel,
        title: 'Batch Comparison',
        config: undefined,
        isConfigured: false,
        hasError: false
      }]);
      setLayout(prev => [...prev, {
        i: batchPanelId,
        x: 0,
        y: 0,
        w: PLC_PANEL_SIZES.DEFAULT.w,
        h: 9,
        minW: PLC_PANEL_SIZES.MIN.w,
        minH: PLC_PANEL_SIZES.MIN.h
      }]);
      setNextId(prev => prev + 1);
    } else {
      batchPanelId = batchPanel.id;
    }

    // Add PhaseIdentificationPanel if it doesn't exist
    if (!phasePanel) {
      phasePanelId = `plc-panel-${nextId + (batchPanel ? 0 : 1)}`;
      setItems(prev => [...prev, {
        id: phasePanelId,
        type: PLCComponentType.PhaseIdentificationPanel,
        title: 'Phase Comparison',
        config: undefined,
        isConfigured: false,
        hasError: false
      }]);
      setLayout(prev => [...prev, {
        i: phasePanelId,
        x: 6,
        y: 0,
        w: PLC_PANEL_SIZES.DEFAULT.w,
        h: 9,
        minW: PLC_PANEL_SIZES.MIN.w,
        minH: PLC_PANEL_SIZES.MIN.h
      }]);
      setNextId(prev => prev + (batchPanel ? 1 : 2));
    } else {
      phasePanelId = phasePanel.id;
    }

    try {
      // Prepare payload as in PLCPanelConfigDrawer
      const startDateTime = dayjs(selection.timeRange.startTime);
      const endDateTime = dayjs(selection.timeRange.endTime);
      const systems = getSystemNames();
      const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';

      // Prepare payloads for both APIs
      const batchApiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
        batch_ids: selection.batchIds,
        x_axis: 'DateTime',
        y_axis: [],
        identifier: getBatchIdentifier(),
        compare_limit: selection.batchIds.length,
        system: systemNames,
        ...(appliedFilters && { filters: formatQuery(appliedFilters, 'sql') })
      };

      const phaseApiPayload = selection.batchIds.length > 0 ? {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
        batch_ids: selection.batchIds,
        system: systemNames || 'VAT',
        x_axis: 'DateTime',
        phase_identifier: getPhaseIdentifier(),
        batch_identifier: getBatchIdentifier(),
        ...(appliedFilters && { filters: formatQuery(appliedFilters, 'sql') })
      } : null;

      // Call APIs individually and process responses immediately
      // Start batch comparison API call and process response immediately
      const batchApiCall = postRequest('/file/explore-plc/batch-comparison', batchApiPayload)
        .then(response => {
          setBatchComparisonLoading(false);

          // Process batch comparison response immediately
          const batchApiData = response.data.data;
          let columnOptions = {};
          let availableColumns: string[] = [];
          let firstColumn = null;

          // Check if data is valid
          if (batchApiData && batchApiData.columnOptions && Object.keys(batchApiData.columnOptions).length > 0) {
            columnOptions = batchApiData.columnOptions || {};
            availableColumns = Object.keys(columnOptions);
            firstColumn = availableColumns.length > 0 ? availableColumns[0] : null;

            // Success case - update panel with data
            const batchConfig = {
              dataRange: {
                startDate: startDateTime.format('YYYY-MM-DD'),
                endDate: endDateTime.format('YYYY-MM-DD'),
                startTime: startDateTime.format('HH:mm'),
                endTime: endDateTime.format('HH:mm')
              },
              basic: {
                xAxisColumn: 'DateTime',
                selectedColumns: {
                  indices: firstColumn ? [0] : [],
                  headers: firstColumn ? [firstColumn] : []
                },
                group: 'BatchId'
              },
              advanced: { windowMode: false },
              apiData: {
                ...batchApiData,
                selectedBatches: selection.batchIds,
                availableBatches: batchApiData.availableBatches || selection.batchIds,
                qualityBatchIds: batchApiData.qualityBatchIds || selection.batchIds.map(id => ({
                  batch_id: id,
                  batch_quality: 8.5,
                  is_batch_good: true
                })),
                compareLimit: batchApiData.compareLimit || selection.batchIds.length,
                columns: availableColumns,
                all_features: batchApiData.metadata.all_features || []
              },
              title: firstColumn ? `Batch Comparison - ${firstColumn}` : 'Batch Comparison - No Data Selected',
              panelType: PLCComponentType.BatchComparisonPanel,
              lastModified: new Date().toISOString()
            };

            setPanelConfigurations(prev => ({ ...prev, [batchPanelId]: batchConfig }));
            setItems(prev => prev.map(item =>
              item.id === batchPanelId
                ? { ...item, config: batchConfig, isConfigured: true, hasError: false, errorMessage: undefined }
                : item
            ));
          } else {
            // No data case - update panel with error state
            console.warn('Batch comparison API returned empty data');
            const errorMessage = 'Batch Comparison API returned no data';
            setItems(prev => prev.map(item =>
              item.id === batchPanelId
                ? { ...item, config: undefined, isConfigured: true, hasError: true, errorMessage }
                : item
            ));
          }

          return response;
        })
        .catch(error => {
          setBatchComparisonLoading(false);

          // Error case - update panel with error state
          console.error('Failed to fetch batch comparison data', error);
          const errorMessage = `Batch Comparison API Failed: ${error?.message || 'Failed to fetch batch comparison data'}`;
          setItems(prev => prev.map(item =>
            item.id === batchPanelId
              ? { ...item, config: undefined, isConfigured: true, hasError: true, errorMessage }
              : item
          ));

          throw error;
        });

      // Start phase identification API call if needed and process response immediately
      let phaseApiCall: Promise<any> | null = null;
      if (phaseApiPayload) {
        phaseApiCall = postRequest('/file/explore-plc/phase-identification', phaseApiPayload)
          .then(response => {
            setPhaseIdentificationLoading(false);

            // Process phase comparison response immediately
            try {
              const phaseApiData = response.data.data;

              // Check if phase data is valid
              if (!phaseApiData || !phaseApiData.metadata) {
                throw new Error('Phase comparison API returned empty data');
              }

              const firstBatchId = selection.batchIds[0];

              const phaseConfig = {
                dataRange: {
                  startDate: startDateTime.format('YYYY-MM-DD'),
                  endDate: endDateTime.format('YYYY-MM-DD'),
                  startTime: startDateTime.format('HH:mm'),
                  endTime: endDateTime.format('HH:mm')
                },
                basic: {
                  xAxisColumn: 'DateTime',
                  selectedColumns: {
                    indices: [0],
                    headers: [phaseApiData.metadata?.y_axis || 'Temperature']
                  },
                  group: 'None'
                },
                advanced: { windowMode: false },
                apiData: {
                  ...phaseApiData,
                  selectedBatches: selection.batchIds,
                  availableBatches: selection.batchIds, // Use selection batchIds directly
                  qualityBatchIds: selection.batchIds.map(id => ({
                    batch_id: id,
                    batch_quality: 8.5,
                    is_batch_good: true
                  })),
                  compareLimit: selection.batchIds.length,
                  columns: phaseApiData?.metadata?.all_features || ['Temperature'],
                  all_features: phaseApiData?.metadata?.all_features || ['Temperature'],
                  batchId: firstBatchId,
                  system: systemNames || 'VAT',
                  xAxis: 'DateTime',
                  yAxis: phaseApiData.metadata?.y_axis || 'Temperature',
                  identifier: getBatchIdentifier(),
                  phase_identifier: getPhaseIdentifier()
                },
                title: 'Phase Comparison',
                panelType: PLCComponentType.PhaseIdentificationPanel,
                lastModified: new Date().toISOString()
              };

              setPanelConfigurations(prev => ({ ...prev, [phasePanelId]: phaseConfig }));
              setItems(prev => prev.map(item =>
                item.id === phasePanelId
                  ? { ...item, config: phaseConfig, isConfigured: true, hasError: false, errorMessage: undefined }
                  : item
              ));
            } catch (phaseError: any) {
              console.error('Failed to process phase comparison data', phaseError);
              const errorMessage = `Phase Comparison Processing Error: ${phaseError?.message || 'Failed to process phase comparison data'}`;
              setItems(prev => prev.map(item =>
                item.id === phasePanelId
                  ? { ...item, config: undefined, isConfigured: true, hasError: true, errorMessage }
                  : item
              ));
            }

            return response;
          })
          .catch(error => {
            setPhaseIdentificationLoading(false);

            // API failed - update panel with error state
            console.error('Failed to fetch phase comparison data', error);
            const errorMessage = `Phase Comparison API Failed: ${error?.message || 'Failed to fetch phase comparison data'}`;
            setItems(prev => prev.map(item =>
              item.id === phasePanelId
                ? { ...item, config: undefined, isConfigured: true, hasError: true, errorMessage }
                : item
            ));

            throw error;
          });
      } else {
        setPhaseIdentificationLoading(false);
        // No phase response but batches were selected - show error
        const errorMessage = 'Phase Comparison API was not called';
        setItems(prev => prev.map(item =>
          item.id === phasePanelId
            ? { ...item, config: undefined, isConfigured: true, hasError: true, errorMessage }
            : item
        ));
      }

      // Wait for both APIs to complete (responses are processed immediately in their respective .then() handlers)
      await Promise.allSettled([batchApiCall, ...(phaseApiCall ? [phaseApiCall] : [])]);
    } catch (error) {
      console.error('Failed to fetch batch comparison data', error);
      // Ensure loading states are cleared on error
      setBatchComparisonLoading(false);
      setPhaseIdentificationLoading(false);
    } finally {

      // Notify parent about panel changes after API calls complete
      if (onPanelsChange) {
        // Use setTimeout to ensure state updates have completed
        setTimeout(() => {
          setItems(currentItems => {
            const newActivePanels = currentItems.map(item => item.type);
            onPanelsChange(newActivePanels);
            return currentItems; // Don't modify items, just trigger the callback
          });
        }, 100);
      }
    }
  }, [items, nextId, setItems, setLayout, setPanelConfigurations, setNextId, getSystemNames, onPanelsChange]);


  return (
    <>
      <div className="plc-content view-content flex-1 h-full relative flex flex-col">
        {/* Main Drag & Drop Section */}
        <div 
          className="plc-grid-section p-4"
          style={{
            height: explorerCollapsed ? '100%' : '65%',
            transition: 'height 0.3s ease-in-out',
            overflow: 'hidden'
          }}
        >
          <PLCGridLayout
            ref={gridLayoutRef}
            items={items}
            layout={layout}
            onLayoutChange={handleLayoutChange}
            onItemsChange={handleItemsChange}
            onDrop={handleDrop}
            onPanelRemove={handlePanelRemove}
            onOpenConfiguration={handleOpenConfiguration}
            onCreatePhaseIdentification={handleCreatePhaseIdentification}
            phaseComparisonLoading={phaseComparisonLoading}
            batchComparisonLoading={batchComparisonLoading}
            phaseIdentificationLoading={phaseIdentificationLoading}
            qualityLookup={qualityLookup}
          />
        </div>

        {/* PLCDataExplorer Section */}
        <div 
          className="plc-explorer-section"
          style={{
            height: explorerCollapsed ? 'auto' : '45%',
            transition: 'height 0.3s ease-in-out',
            position: 'relative',
            zIndex: 10
          }}
        >
          <PLCDataExplorer
            isCollapsed={explorerCollapsed}
            onToggleCollapse={handleExplorerToggleCollapse}
            onDataLoad={handleExplorerDataLoad}
            onSelectionExplore={handleSelectionExplore}
            onFiltersChange={(filters) => setAppliedFilters(filters)}
            onSidebarCollapse={onSidebarCollapse}
          />
        </div>
      </div>

      {/* Configuration Drawers */}
      {getCurrentPanelType() === PLCComponentType.BatchComparisonPanel && (
        <PLCPanelConfigDrawer
          open={drawerOpen}
          onClose={handleDrawerClose}
          panelId={selectedPanelId}
          configuration={getCurrentConfiguration()}
          availableFeatures={[]} // Will be populated with real features later
          onConfigurationSave={handleConfigurationSave}
          qualityLookup={qualityLookup}
        />
      )}

      {getCurrentPanelType() === PLCComponentType.PhaseIdentificationPanel && (
        <PhaseIdentificationConfigDrawer
          open={drawerOpen}
          onClose={handleDrawerClose}
          panelId={selectedPanelId}
          configuration={getCurrentConfiguration()}
          onConfigurationSave={handleConfigurationSave}
        />
      )}
    </>
  );
};

export default PLCContent;
