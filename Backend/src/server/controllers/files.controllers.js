import { sequelize } from "../database/dbConnection.js";
import { generateResponse } from "../utils/commonResponse.js";
import path from "path";
import fs from 'fs';
import CSVFilesSchema from "../models/file.model.js"
import User from "../models/user.model.js";
import csv from 'csv-parser';
import crypto from 'crypto';
import bcrypt from 'bcrypt';
import WorkflowComponents from "../models/workflowComponents.model.js";
import CsvMapping from "../models/csvMapping.model.js";
import { createObjectCsvWriter } from 'csv-writer';
import 'dotenv/config';
import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand, HeadObjectCommand, ListObjectsV2Command, DeleteObjectsCommand } from "@aws-sdk/client-s3";
import { uploadFileToS3, deleteFile, listFiles, getFileStream } from "../s3 Services/s3Services.js";
import { v4 as uuidv4 } from 'uuid';
import Tenants from "../models/tenants.model.js";
import Systems from "../models/systems.model.js";
import SystemMeta from "../models/systemMeta.model.js";
import { Op, literal } from "sequelize";
import axios from 'axios';
import MLJob from "../models/MLjobs.model.js";
import mongoose from 'mongoose';
import { getCache, setCache, generateCacheKey } from '../cache/cacheUtils.js';
import { transformPanelData, filterPanelData } from "../utils/panelTransform.js";
import { start } from "repl";

// const s3 = new S3Client({
//   region: process.env.AWS_REGION,
//   credentials: {
//     accessKeyId: process.env.AWS_ACCESS_KEY_ID,
//     secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
//   },
// });

//Common function for material processing
const processMaterialFile = async (fileUrl, fileKeyToSave, fileTypes) => {
  try {
    
    const mlJobId = new mongoose.Types.ObjectId();

    const baseUrlPath = fileUrl.substring(0, fileUrl.lastIndexOf('/'));
    const modelLoadPath = `${baseUrlPath}/model`;
    
    // Create ML job with initial payload
    const mlJobPayload = {
      operation: "identification_model",
      clustering: {},
      datasource: {
        file_path: fileUrl,
        file_save_path: fileUrl,
        feature_filters: {},
        model_load_path: modelLoadPath,
        exclude_features: [],
      },
      workflow_id: 0,
      execution_id: mlJobId.toString(),
      dimensionality_reduction: {},
    };

    //Save ML job to MongoDB with same ObjectId
    const mlJob = await MLJob.create({
      _id: mlJobId,
      workflow_id: 0,
      settings: mlJobPayload,
      actual_settings: {},
      ml_response: {},
      workflow_type: "material_file",
      status: "pending",
      file_id: fileKeyToSave,
    });

    //Call material clustering API
    const response = await axios.post(
      `${process.env.AIML_API}/input_material_clustering_inference`,
      mlJobPayload
    );
    
    return true;
  } catch (error) {
    console.error('Error in material processing:', error);
    return false;
  }
};

const uploadFile = async (req, res) => {
    const {id} = req.user
    const {tenant_id} = req.user

    try {
      console.log('req.file', req.file)
      const systems = Array.isArray(req.body.systems) ? req.body.systems: JSON.parse(req.body.systems || '[]');
        const filePath = req.file.path;
        // const fileKey = `${req.file.originalname}`;
        let systemNames = []
        if(systems && systems.length){
          systemNames = systems.map(item => item.systemName);
        }
        const folderName = systemNames.join(" + ");

        let fileKey = uuidv4();
        const fileKeyToSave = fileKey

        let tenantData = await Tenants.findOne({ where: {id: tenant_id}})
        let awsCredentials = tenantData.dataValues.aws_credentials
        console.log('---------------',awsCredentials['AWS_FOLDER'])
        if(awsCredentials['AWS_FOLDER'] && awsCredentials['AWS_FOLDER'].trim() !== ''){
          fileKey = `${awsCredentials['AWS_FOLDER']}/${folderName?folderName+'/':""}${fileKey}`

        }else{
          fileKey = `data/data=file/${folderName?folderName+'/':""}${fileKey}`

        }
        const fileUrl = await uploadFileToS3(filePath, fileKey, awsCredentials);

        if(fileUrl){
          // Remove the file from local storage
          fs.unlink(filePath, (err) => {
            if (err) {
              console.error('Error removing file from local storage:', err);
            } else {
              console.log('File removed from local storage:', filePath);
            }
          });
          
          let systemIds = [];
          if(systems && systems.length){
            systemIds = systems.map(item => item.systemId);
          }
          const compatibility = req.body.compatibility;
          
          // Parse the type_of_file from string to array if needed
          let fileTypes;
          try {
            fileTypes = Array.isArray(req.body.type_of_file) 
              ? req.body.type_of_file 
              : JSON.parse(req.body.type_of_file || '["processes"]');
          } catch (e) {
            fileTypes = ['processes']; // Default if parsing fails
          }
          
          const uploadedFile = await CSVFilesSchema.create({
              user_id: id,
              file_name: req.file.originalname,
              path_for_aiml: process.env.FILE_PATH_FOR_AIML + req.file.filename,
              file_path: fileKey,
              description: req.body.description || null,
              version: req.body.version || 1,
              tenant_id:tenant_id,
              systems_id : systemIds,
              compatibility: (id==1 || id ==46) ? true : compatibility,  // id 46 is of kalprish statically added compatibility true
              csv_id: fileKeyToSave,
              aws_file_link: fileUrl,
              parent_file_id: req.body.parent_file_id || null,
              type_of_file: fileTypes,
              clustering_data_response: (fileTypes.includes('material') && compatibility == 'true') ? 'in_progress' : null,
          });

          //Check if file type contains 'material' and create ML job
          
          if (fileTypes.includes('material') && compatibility=='true') {
            
            await processMaterialFile(fileUrl, fileKeyToSave, fileTypes);
          }
          
          const fileResponse = {
              csvId: uploadedFile.get('csv_id'),
          }
          return generateResponse(res, 200, 'File uploaded successfully.', uploadedFile)
        }
        else{
          return generateResponse(res, 500, 'Failed to upload file.')
        }


    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}

// This function will return the csv file each colum,ns data

const getFile = async (req, res) => {
  try {
      const { csvId } = req.params;
      const fileRecord = await CSVFilesSchema.findOne({
          where: {
              csv_id: csvId,
          }
      });

      if (!fileRecord) {
          return generateResponse(res, 404, 'File not found.')
      }

      const key = fileRecord?.file_path;

      const bucketName = await gets3BucketName(fileRecord.tenant_id);
      const params = { Bucket: bucketName, Key: key };
      const s3Client = await getS3ClientForTenant(fileRecord.tenant_id);

      // Get file from S3
      const { Body } = await s3Client.send(new GetObjectCommand(params));
      const results = [];
      let hasDateTimeColumn = false;

      await new Promise((resolve, reject) => {
          Body.pipe(csv())
              .on("data", (data) => {
                  results.push(data);
                  // Check if DateTime column exists in the first row
                  if (results.length === 1) {
                      hasDateTimeColumn = 'DateTime' in data;
                  }
              })
              .on("end", () => {
                  // Only sort if DateTime column exists
                  if (hasDateTimeColumn) {
                      results.sort((a, b) => {
                          const dateA = new Date(a.DateTime);
                          const dateB = new Date(b.DateTime);
                          return dateA - dateB; // ascending order
                      });
                      resolve();
                  } else {
                      // If no DateTime column, keep original order
                      resolve();
                  }
              })
              .on("error", (error) => {
                  console.error("❌ CSV Parsing Error:", error);
                  reject(error);
              });
      });

      // Send response with appropriate message
      const message = hasDateTimeColumn
          ? "File fetched and sorted by DateTime successfully"
          : "File fetched successfully";

      return generateResponse(res, 200, message, results);
  } catch (error) {
      console.error('error :', error);
      return generateResponse(res, 500, 'Failed to retrieve file.')
  }
}


const getFileList = async (req, res) => {
  const {id , tenant_id} = req.user
    try {
      const systemsIdParam = req.query?.systems_id;

      // Extract the shared workflows and workflowStructre
      const workflowStructures = await sequelize.query(
        `
        select  w2."hierarchy" from workflow_shared ws 
        inner join workflows w ON CAST(w.id AS VARCHAR) = ws.share_type_id
        inner join workflowstructures w2 on w2.workflow_id = w.id
        where (
          (ws.share_type = 'tenant' AND ws.shared_to_id = ${tenant_id}) OR
          (ws.share_type = 'users' AND (ws.shared_to_id = ${id} OR ws.user_id = ${id}))
        )
        `,
        {
          type: sequelize.QueryTypes.SELECT,
        }
      );

      let dataIds = []
      if (workflowStructures) {  // Extract the csv_id of those files which are included in the the sharable workflow
         dataIds = workflowStructures
          ?.flatMap(item => 
            item?.hierarchy?.nodes?.filter(node => node?.data?.type === "file")
          )
          .map(node => node?.data?.id);
      }

      const whereConditions = {
        [Op.or]: [
          {
            [Op.and]: [
              { user_id: id },
              { is_deleted: false },
              { hide_for_s3: false }
            ]
          },
        ]
      };
      if (dataIds.length > 0) {
        whereConditions[Op.or].push({ csv_id: { [Op.in]: dataIds } });
      }

      if (systemsIdParam && typeof systemsIdParam === 'string') {
        const systemsNameArray = systemsIdParam.split(',').map((name) => name.trim());
        
        if (systemsNameArray.length > 0) {
          const sortedSystemsNameArray = systemsNameArray.sort();
          const arrayLiteral = `{${sortedSystemsNameArray.join(',')}}`;
      
          whereConditions.systems_name =  sequelize.literal(
              `"CSVFiles"."systems_id" @> '${arrayLiteral}' AND "CSVFiles"."systems_id" <@ '${arrayLiteral}'`
            );
        }
      }



      const files = await CSVFilesSchema.findAll({
        attributes: [
          'csv_id', 
          'file_name', 
          'file_path', 
          'version',
          'compatibility', 
          'created_at', 
          'updated_at', 
          'parent_file_id', 
          'type_of_file',
          'clustering_data_response'
        ],
        where: whereConditions,
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['first_name', 'last_name']
              }
          ],
        order: [['created_at', 'DESC']]
      });
  
      if (!files.length) {
        return generateResponse(res, 200, 'No files are available', { files: [] });
      }
  
      const filesWithColumns = await Promise.all(files.map(async (file) => {
        return {
          csv_id: file.csv_id,
          file_name: file.file_name,
          file_path: file.file_path,
          version: file.version,
          owner:file.user,
          compatibility:file.compatibility,
          created_at: file.created_at,
          updated_at: file.updated_at,
          parent_file_id: file.parent_file_id || null,
          type_of_file: file.type_of_file,
          clustering_data_response: file.clustering_data_response,
          // columns: columns // Add columns to response
        };
      }));
  
      const response = {
        files: filesWithColumns
      };
      return generateResponse(res, 200, 'File list is fetched successfully', response);
  
    } catch (error) {
      console.error('Error fetching files:', error);
      return generateResponse(res, 500, 'Failed to fetch files');
    }
  };


  const getFileColumns = async (req, res) => {
    try {
      const { csvId } = req.params;
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(csvId)) {
        return generateResponse(res, 404, `File with csvid ${csvId} not found`);
      }
      const file = await CSVFilesSchema.findOne({
        attributes: ['csv_id', 'file_name', 'file_path', 'version', 'tenant_id', 'type_of_file'],
        where: { csv_id: csvId }
      });
      if (!file) {
        return generateResponse(res, 404, `File with csvid ${csvId} not found`);
      }
  
      const key = file?.file_path;

      const bucketName = await gets3BucketName(file?.tenant_id);
      const params = { Bucket: bucketName, Key: key };
      const s3Client = await getS3ClientForTenant(file?.tenant_id);
      const { Body } = await s3Client.send(new GetObjectCommand(params));
  
      let columns = [];
      let firstThreeRows = [];
      let datetimeFormats = {};
  
      const processFile = new Promise((resolve, reject) => {
        const stream = Body.pipe(csv());
  
        stream.on('headers', (headers) => {
          columns = headers;
        });
  
        stream.on('data', (row) => {
          if (firstThreeRows.length < 3) {
            firstThreeRows.push(row);
            
            if (row?.DateTime) {
              const dateOnly = row.DateTime.split(' ')[0]; // Extract YYYY-MM-DD only
              const detectedFormat = detectDateFormat(dateOnly);
              datetimeFormats[detectedFormat] = (datetimeFormats[detectedFormat] || 0) + 1;
            }
          }
        });
  
        stream.on('end', () => {
          const mostCommonFormat = Object.entries(datetimeFormats).sort((a, b) => b[1] - a[1])[0]?.[0] || null;
          resolve({ columns, firstThreeRows, mostCommonFormat });
        });
  
        stream.on('error', (error) => {
          console.error('Error reading file:', error);
          reject(error);
        });
      });
  
      let { columns: columnsData, firstThreeRows: rowsData, mostCommonFormat } = await processFile;
      let response = {
        file,
        columns: columnsData,
        mostCommonDateTimeFormat: mostCommonFormat
      };
      return generateResponse(res, 200, 'Successfully fetched file columns and datetime format', response);
  
    } catch (error) {
      console.log('error :', error);
      return generateResponse(res, 500, 'Something went wrong');
    }
  };

  const detectDateFormat = (dateString) => {
    const regexFormats = [
      { regex: /^\d{4}-\d{2}-\d{2}$/, format: 'YYYY-MM-DD' },
      { regex: /^\d{2}\/\d{2}\/\d{4}$/, format: 'MM/DD/YYYY' },
      { regex: /^\d{2}-\d{2}-\d{4}$/, format: 'DD-MM-YYYY' }
    ];
  
    for (let { regex, format } of regexFormats) {
      if (regex.test(dateString)) {
        return format;
      }
    }
    return 'Unknown Format';
  };

  const removeFile = async (req, res) => {
    const { csv_id } = req.params;

    try {
        const fileRecord = await CSVFilesSchema.findOne({ where: { csv_id } });

        if (!fileRecord) {
            return res.status(404).json({ message: 'File not found' });
        }

        const referencedWorkflows = await WorkflowComponents.findAll({
            where: { component: csv_id },
            attributes: ['workflow_id']
        });

        if (referencedWorkflows.length > 0) {
            await CSVFilesSchema.update(
                { is_deleted: true },
                { where: { csv_id } }
            );

            const workflowIds = referencedWorkflows.map((record) => record.workflow_id);
            return res.status(200).json({
                message: 'File is referenced in workflows and has been soft deleted',
                status: 200,
                workflowIds,
            });
        }

        const bucketName = await gets3BucketName(fileRecord.tenant_id);
        console.log('bucketName', bucketName)

        // Delete the file from S3
        const deleteParams = {
            Bucket: bucketName,
            Key: fileRecord.file_path,
        };

        const s3Client = await getS3ClientForTenant(fileRecord?.tenant_id);

        await s3Client.send(new DeleteObjectCommand(deleteParams));

        await CSVFilesSchema.destroy({ where: { csv_id } });

        return res.status(200).json({
            message: 'File successfully deleted',
            status: 200
        });
    } catch (error) {
        console.error('Error deleting file:', error);
        return res.status(500).json({ message: 'Internal server error', error });
    }
};


const mapCsvFields = async (req, res) => {
  try {
    const { csvId } = req.params;
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(csvId)) {
      return generateResponse(res, 404, `File with csvid ${csvId} not found`);
    }
    
    if (!req.body?.headerMapping) {
      return generateResponse(res, 422, 'Missing fields headerMapping');
    }
    
    const headerMapping = req.body.headerMapping;

    const fileRecord = await CSVFilesSchema.findOne({
      where: { csv_id: csvId }
    });

    if (!fileRecord) {
      return generateResponse(res, 404, `File with csvid ${csvId} not found`);
    }
    const bucketName = await gets3BucketName(fileRecord.tenant_id);

    const key = fileRecord?.file_path;
    const params = { Bucket: bucketName, Key: key };

    const s3Client = await getS3ClientForTenant(fileRecord?.tenant_id);

    // ✅ Get file from S3
    const { Body } = await s3Client.send(new GetObjectCommand(params));

    // Read and parse the CSV file
    const rows = [];
    let originalHeaders = [];

    await new Promise((resolve, reject) => {
      Body.pipe(csv())
        .on('headers', (headerList) => {
          originalHeaders = headerList; // Store original headers
        })
        .on('data', (row) => {
          rows.push(row); // Collect row data as-is
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // ✅ Ensure headers are renamed correctly
    const newHeaders = originalHeaders.map((header) => headerMapping[header] || header);

    // ✅ Prepare CSV writer with updated headers
    const csvWriter = createObjectCsvWriter({
      path: `/tmp/${fileRecord.csv_id}.csv`,
      header: newHeaders.map((newHeader, index) => ({
        id: originalHeaders[index], // Keep original field mapping
        title: newHeader, // Rename only the title
      })),
    });

    // ✅ Write records back to the CSV file
    await csvWriter.writeRecords(rows);

    // ✅ Upload the modified file back to S3
    const modifiedFilePath = `/tmp/${fileRecord.csv_id}.csv`;
    const modifiedFileContent = fs.readFileSync(modifiedFilePath);
    const uploadParams = {
      Bucket: bucketName,
      Key: key,
      Body: modifiedFileContent,
    };

    await s3Client.send(new PutObjectCommand(uploadParams));

    // ✅ Clean up the temporary file
    if (fs.existsSync(modifiedFilePath)) {
      fs.unlinkSync(modifiedFilePath);
    }

    // ✅ Reverse mapping for database storage
    const reversedMapping = Object.fromEntries(
      Object.entries(headerMapping).map(([key, value]) => [value, key])
    );

    try {
      // Update compatibility in CSVFilesSchema
      await CSVFilesSchema.update(
        { compatibility: true },
        { where: { csv_id: csvId } }
      );

      // Upsert columns mapping in CsvMapping table
      const existingRow = await CsvMapping.findOne({
        where: { tenant_id: req.user.tenant_id },
      });

      if (existingRow) {
        const updatedColumnsMapping = {
          ...existingRow.columns_mapping,
          ...reversedMapping,
        };
        await CsvMapping.update(
          { columns_mapping: updatedColumnsMapping },
          { where: { tenant_id: req.user.tenant_id } }
        );
      } else {
        await CsvMapping.create({
          tenant_id: req.user.tenant_id,
          columns_mapping: reversedMapping,
        });
      }

      // After setting compatibility to true hit the AI/Ml API
      if (fileRecord.type_of_file.includes('material')) {
        await processMaterialFile(fileRecord.aws_file_link, fileRecord.csv_id, fileRecord.type_of_file);
      }

      // Remove the local file
      fs.unlink(filePath, (err) => {
        if (err) {
          console.error("Error removing file from local storage:", err);
        } else {
          console.log("File removed from local storage:", filePath);
        }
      });

      return generateResponse(res, 200, 'CSV Mapping updated successfully');
    } catch (error) {
      console.error('Error upserting CSV mapping:', error);
      return generateResponse(res, 500, 'Error upserting CSV mapping:');
    }
  } catch (error) {
    console.error(error);
    return generateResponse(res, 500, 'Internal server error while CSV Mapping');
  }
};



const csvMappingHistory = async (req , res) =>{
  try {
    const {tenant_id} = req.user
    const columns = req.body?.columns ? req.body.columns : [];
    if(!columns.length){
      return generateResponse(res, 400, 'Missing required parameter columns')
    }
    const CsvMappingResults = await CsvMapping.findAll({
      where:{tenant_id : tenant_id},
      attributes: columns.map(column => [
        sequelize.json(`columns_mapping.${column}`),
        column,
      ]),
    });
    return generateResponse(res, 200, 'CSV Mapping History fetched successfully',CsvMappingResults)
  } catch (error) {
  console.log('CSV Mapping History fetched error :', error);
  return generateResponse(res, 500, 'CSV Mapping History fetched server error')
    
  }
}

const updateFile = async (req, res) => {
  const { csv_id } = req.body;
  const { tenant_id, id } = req.user;

  try {
    // Find the existing file record by csv_id
    const fileRecord = await CSVFilesSchema.findOne({
      where: { csv_id, tenant_id },
    });

    if (!fileRecord) {
      return generateResponse(res, 404, "File not found");
    }

    // Get tenant AWS credentials
    let tenantData = await Tenants.findOne({ where: { id: tenant_id } });
    let awsCredentials = tenantData.dataValues.aws_credentials;

    // Generate new S3 file key
    let fileKey = uuidv4();
    if (
      awsCredentials["AWS_FOLDER"] &&
      awsCredentials["AWS_FOLDER"].trim() !== ""
    ) {
      fileKey = `${awsCredentials["AWS_FOLDER"]}/${fileKey}`;
    } else {
      fileKey = `data/data=file/${fileKey}`;
    }

    // Upload the new file to S3
    const filePath = req.file.path;
    const fileUrl = await uploadFileToS3(filePath, fileKey, awsCredentials);

    if (!fileUrl) {
      return generateResponse(res, 500, "Failed to upload file to S3");
    }

    // Delete the old file from S3 if it exists
    try {
      if (fileRecord.file_path) {
        const s3Storage = new S3Client({
          region: awsCredentials?.AWS_REGION,
        });

        const deleteParams = {
          Bucket: awsCredentials?.AWS_BUCKET_NAME,
          Key: fileRecord.file_path,
        };

        await s3Storage.send(new DeleteObjectCommand(deleteParams));
        console.log("Old file deleted from S3:", fileRecord.file_path);
      }
    } catch (deleteError) {
      console.error("Error deleting old file from S3:", deleteError);
    }

    // Update the file record with new data
    const updatedFile = await CSVFilesSchema.update(
      {
        file_name: req.file.originalname,
        file_path: fileKey,
        path_for_aiml: process.env.FILE_PATH_FOR_AIML + req.file.filename,
        version: fileRecord.version + 1, // Increment version
        description: req.body.description || fileRecord.description,
        aws_file_link: fileUrl,
      },
      { where: { csv_id } }
    );

    if (updatedFile) {
      // Remove the local file
      fs.unlink(filePath, (err) => {
        if (err) {
          console.error("Error removing file from local storage:", err);
        } else {
          console.log("File removed from local storage:", filePath);
        }
      });

      return generateResponse(res, 200, "File updated successfully", {
        csvId: csv_id,
      });
    } else {
      return generateResponse(res, 500, "Failed to update file");
    }
  } catch (error) {
    console.error("Error updating file:", error);
    return generateResponse(res, 500, "Failed to update file");
  }
};




const createClusterAllRunCSVFile = async (req, res) => {
  try {
    let UserSystemSelected = await User.findOne({ where: { id: req.user.id } });
    const selected_systems = UserSystemSelected.selected_systems;
    const getAllSystems = await Systems.findAll({});
    const selectedNames = selected_systems
      .map(id => getAllSystems.find(system => system.id == id)?.name)
      .filter(Boolean)
      .join(" + ");

    console.log("selectedNames", selectedNames);
    const { id, tenant_id } = req.user;
    const { clusterAllRunData, fileName, fileId } = req.body;

    if (!clusterAllRunData) {
      return generateResponse(res, 404, 'Cluster all run data is not available.');
    }

    const filePath = Date.now();
    const outputFilePath = path.join(process.cwd(), 'public', 'datasource', `${filePath}.csv`);

    const headers = Object.keys(clusterAllRunData);
    const rowIndices = Array.from(new Set(
      headers.flatMap(header => Object.keys(clusterAllRunData[header]).map(Number))
    )).sort((a, b) => a - b);
    const rows = rowIndices.map(index =>
      headers.map(header => clusterAllRunData[header][index] ?? '').join(',')
    );
    const csvContent = [headers.join(','), ...rows].join('\n');

    fs.writeFile(outputFilePath, csvContent, 'utf8', async (err) => {
      if (err) {
        console.error('Error writing Cluster all run CSV file:', err);
        return generateResponse(res, 500, 'Error writing Cluster all run CSV file:');
      } else {
        try {
          const systems = Array.isArray(req.body.systems) ? req.body.systems : JSON.parse(req.body.systems || '[]');
          let systemIds = [];
          if (systems && systems.length) {
            systemIds = systems.map(item => item.systemId);
          }
          const compatibility = true;

          // Upload the file to S3
          let fileKey = uuidv4();
          const fileKeyToSave = fileKey
  
          let tenantData = await Tenants.findOne({ where: {id: tenant_id}})
          let awsCredentials = tenantData.dataValues.aws_credentials
  
          // fileKey = `data/data=file/${folderName}/${fileKey}`
          fileKey = `data/data=file/${selectedNames}/${fileKey}`
          
          const fileUrl = await uploadFileToS3(outputFilePath, fileKey, awsCredentials);

          if (fileUrl) {
            // Remove the file from local storage
            fs.unlink(outputFilePath, (err) => {
              if (err) {
                console.error('Error removing file from local storage:', err);
              } else {
                console.log('File removed from local storage:', outputFilePath);
              }
            });

            const uploadedFile = await CSVFilesSchema.create({
              user_id: id,
              file_name: fileName,
              path_for_aiml: process.env.FILE_PATH_FOR_AIML + `${filePath}.csv`,
              file_path: fileKey,
              description: 'File is created and uploaded using cluster run data' || null,
              version: req.body.version || 1,
              tenant_id: tenant_id,
              systems_id: systemIds,
              compatibility: compatibility,
              csv_id: fileKeyToSave,
              aws_file_link: fileUrl,
              parent_file_id: fileId,
              type_of_file: req.body.type_of_file
            });

            const fileResponse = {
              csvId: uploadedFile.get('csv_id'),
            };
            return generateResponse(res, 200, 'Cluster all run File created & uploaded successfully.', fileResponse);
          } else {
            return generateResponse(res, 500, 'Failed to upload Cluster all run file.');
          }
        } catch (error) {
          console.log('error :', error);
          return generateResponse(res, 500, 'Failed to upload Cluster all run file.');
        }
      }
    });
  } catch (error) {
    console.log('error :', error);
    return generateResponse(res, 500, "Error while uploading Cluster all run CSV file:");
  }
};

const getS3ClientForTenant = async (tenantId) => {
  try {
    // Fetch the tenant record
    const tenant = await Tenants.findOne({
      where: { id: tenantId },
      attributes: ["aws_credentials"], // Fetch only the credentials field
    });

    if (!tenant || !tenant.aws_credentials) {
      throw new Error("AWS credentials not found for tenant.");
    }
    const AWS_REGION = tenant?.aws_credentials?.AWS_REGION;

    if ( !AWS_REGION) {
      throw new Error("Invalid AWS credentials format.");
    }

    // Initialize and return the S3 client
    return new S3Client({
      region: AWS_REGION,
      // credentials: {
      //   accessKeyId: AWS_ACCESS_KEY_ID,
      //   secretAccessKey: AWS_SECRET_ACCESS_KEY,
      // },
    });
  } catch (error) {
    console.log('errorrrr', error)
    console.error("Error initializing S3 client:", error.message);
    throw error;
  }
};

const gets3BucketName = async (tenantId) => {
  try {
    // Fetch the tenant record
    const tenant = await Tenants.findOne({
      where: { id: tenantId },
      attributes: ["aws_credentials"], // Fetch only the credentials field
    });

    console.log('tenant', tenant)
    if (!tenant || !tenant.aws_credentials) {
      throw new Error("AWS credentials not found for tenant.");
    }

    // Parse AWS credentials (assuming stored as JSON)
    let AWS_BUCKET_NAME = tenant?.aws_credentials?.AWS_BUCKET_NAME

    if (!AWS_BUCKET_NAME) {
      throw new Error("Invalid AWS credentials format.");
    }

    // Initialize and return the S3 client
    return AWS_BUCKET_NAME
  } catch (error) {
    console.error("Error initializing S3 bucket client:", error.message);
    throw error;
  }
};

const getReportData = async (req, res) => {
  try {
    const { start_date, end_date, tableName } = req.body; // Get start and end dates from the request body

    // Validate the input dates
    if (!start_date || !end_date) {
      return generateResponse(res, 400, "Start date and end date are required.");
    }

    // Get the column data types for the table
    const columnInfo = await sequelize.query(
      `
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = :tableName
      `,
      {
        replacements: { tableName },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Query the report_data table for records within the date range
    const reportData = await sequelize.query(
      `
      SELECT *
      FROM ${tableName}
      WHERE "DateTime" BETWEEN :start_date AND :end_date
      `,
      {
        replacements: { start_date, end_date },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    // Check if data exists
    if (!reportData.length) {
      return generateResponse(res, 200, "No data found for the selected date range.", []);
    }

    // Format the column info to include data types
    const columnsWithTypes = columnInfo.map((column) => ({
      columnName: column.column_name,
      dataType: column.data_type,
    }));

    // Return the fetched data along with column data types
    return generateResponse(res, 200, "Report data fetched successfully.", {
      data: reportData,
      columns: columnsWithTypes,
    });
  } catch (error) {
    console.error("Error fetching report data:", error);
    return generateResponse(res, 500, "Failed to fetch report data.");
  }
};


const uploadJsonFile = async (req, res) => {
  try {

    let fullJson = req.body.annotations;
    let columnName = req.body.columnName;

    const systems = Array.isArray(req.body.systems) ? req.body.systems: JSON.parse(req.body.systems || '[]');
    let systemNames = []
    if(systems && systems.length){
      systemNames = systems.map(item => item.systemName);
    }
    const folderName = systemNames.join(" + ");
    console.log('folderName', folderName)

    const {tenant_id} = req.user
    let tenantData = await Tenants.findOne({ where: {id: tenant_id}})
    let awsCredentials = tenantData.dataValues.aws_credentials

    // if (!fullJson?.annotations || !Array.isArray(fullJson.annotations)) {
    //   return res.status(400).json({ success: false, message: 'Invalid annotations array.' });
    // }

    const annotationsArray = req.body.annotations;

    const anomalyAnnotations = annotationsArray?.filter(item => item.operationType === 'anomaly');
    const operationAnnotations = annotationsArray?.filter(item => item.operationType === 'operation');
    
    const s3Storage = new S3Client({
      region: awsCredentials?.AWS_REGION,
    });
    const bucketName = awsCredentials?.AWS_BUCKET_NAME;

    const anomalyKey = `data/data=file/${folderName}/${columnName}/anomaly.json`;
    const operationKey = `data/data=file/${folderName}/${columnName}/operation.json`;

    const deleteIfExists = async (key) => {
      try {
        await s3Storage.send(new HeadObjectCommand({
          Bucket: bucketName,
          Key: key
        }));
        // If HeadObjectCommand succeeds, file exists - delete it
        await s3Storage.send(new DeleteObjectCommand({
          Bucket: bucketName,
          Key: key
        }));
        console.log(`Deleted existing file: ${key}`);
      } catch (err) {
        if (err.name !== 'NotFound') {
          throw err; // If error is not "NotFound", rethrow
        }
        console.log(`File does not exist: ${key}`);
      }
    };

    // Check and delete existing files
    await deleteIfExists(anomalyKey);
    await deleteIfExists(operationKey);

    // Upload anomaly file
    let upload = await s3Storage.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: anomalyKey,
      Body: JSON.stringify(anomalyAnnotations, null, 2),
    }));
    console.log('upload', upload)

    // Upload operation file
    let upload1 = await s3Storage.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: operationKey,
      Body: JSON.stringify(operationAnnotations, null, 2),
    }));
    console.log('upload1', upload1)

    return res.status(200).json({
      message: 'JSON files uploaded successfully.',
      files: {
        anomalyFile: anomalyKey,
        operationFile: operationKey
      }
    });

  } catch (error) {
    console.error('Error uploading JSON files:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
};


const getJsonFileData = async (req, res) => {
  try {
    const { columnName, systems } = req.body;
    const { tenant_id } = req.user;

    if (!columnName || !systems) {
      return res.status(400).json({ success: false, message: 'Missing columnName or systems.' });
    }

    const systemsArray = Array.isArray(systems)
      ? systems
      : JSON.parse(systems || '[]');

    const folderName = systemsArray.map(item => item.systemName).join(' + ') || 'unknown_system';

    const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
    if (!tenantData) {
      return res.status(404).json({ success: false, message: 'Tenant not found.' });
    }

    const awsCredentials = tenantData.dataValues.aws_credentials;

    const s3 = new S3Client({
      region: awsCredentials.AWS_REGION,
      
    });

    const bucketName = awsCredentials.AWS_BUCKET_NAME;

    const anomalyKey = `data/data=file/${folderName}/${columnName}/anomaly.json`;
    const operationKey = `data/data=file/${folderName}/${columnName}/operation.json`;

    const [anomalyResponse, operationResponse] = await Promise.all([
      s3.send(new GetObjectCommand({ Bucket: bucketName, Key: anomalyKey })),
      s3.send(new GetObjectCommand({ Bucket: bucketName, Key: operationKey }))
    ]);

    const anomalyBuffer = await streamToBuffer(anomalyResponse.Body);
    const operationBuffer = await streamToBuffer(operationResponse.Body);

    const anomalyData = JSON.parse(anomalyBuffer.toString());
    const operationData = JSON.parse(operationBuffer.toString());

    return res.status(200).json({
      success: true,
      data: {
        anomaly: anomalyData,
        operation: operationData
      }
    });

  } catch (error) {
    console.error('Error retrieving JSON files:', error);
    return res.status(500).json({ success: false, message: error.message });
  }
};

const streamToBuffer = async (stream) => {
  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
};

const updateJsonFile = async (req, res) => {

  try {
    const { filename, updatedJsonData } = req.body;

    if (!filename || !updatedJsonData) {
      return res.status(400).json({ success: false, error: 'Missing filename or updatedJsonData in request body.' });
    }

    const tenant_id = req.user.tenant_id;
    const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if (!awsCredentials) {
      throw new Error('AWS credentials not found for the tenant.');
    }

    const bucketName = awsCredentials.AWS_BUCKET_NAME;
    // const bucketName = 'testing-proj12';

    // Initialize S3 client
    const s3Client = new S3Client({
      region: awsCredentials.AWS_REGION,
    });

    // Delete the existing file from S3
    const deleteParams = {
      Bucket: bucketName,
      Key: filename,
    };

    try {
      await s3Client.send(new DeleteObjectCommand(deleteParams));
      console.log('✅ Existing file deleted from S3:', filename);
    } catch (deleteError) {
      console.error('❌ Error deleting file from S3:', deleteError);
      return res.status(500).json({ success: false, error: 'Failed to delete the existing file from S3.' });
    }

    // Upload the updated file to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: filename,
      Body: JSON.stringify(updatedJsonData, null, 2),
    };

    try {
      await s3Client.send(new PutObjectCommand(uploadParams));
      console.log('✅ Updated file uploaded to S3:', filename);
    } catch (uploadError) {
      console.error('❌ Error uploading updated file to S3:', uploadError);
      return res.status(500).json({ success: false, error: 'Failed to upload the updated file to S3.' });
    }

    return res.status(200).json({
      success: true,
      message: 'JSON file updated successfully on S3.',
    });
  } catch (error) {
    console.error('Error updating JSON file on S3:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
};

const deleteJsonFile = async (req, res) => {
  try {
    const { filename } = req.params;

    if (!filename) {
      return res.status(400).json({ success: false, error: 'Filename is required.' });
    }

    const tenant_id = req.user.tenant_id;
    const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
    const awsCredentials = tenantData?.dataValues?.aws_credentials;

    if (!awsCredentials) {
      throw new Error('AWS credentials not found for the tenant.');
    }

    const bucketName = awsCredentials.AWS_BUCKET_NAME;

    // Initialize S3 client
    const s3Client = new S3Client({
      region: awsCredentials.AWS_REGION,
    });

    // Define S3 parameters
    const params = {
      Bucket: bucketName,
      Key: filename,
    };

    // Delete the file from S3
    try {
      await s3Client.send(new DeleteObjectCommand(params));
      console.log('✅ File deleted from S3:', filename);
      return res.status(200).json({
        success: true,
        message: 'File deleted successfully from S3.',
      });
    } catch (deleteError) {
      console.error('❌ Error deleting file from S3:', deleteError);
      return res.status(500).json({ success: false, error: 'Failed to delete the file from S3.' });
    }
  } catch (error) {
    console.error('Error in deleteJsonFile:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
};


/**
 * Handles the exploration of file data for a given CSV file.
 * 
 * This controller function retrieves a CSV file record by its ID, checks for cached results,
 * fetches and parses the file from S3 if necessary, sorts by DateTime if present, filters and
 * transforms the data, and caches the results for future requests.
 * 
 * @async
 * @function
 * @param {import('express').Request} req - Express request object, containing user info, params, query, and body.
 * @param {import('express').Response} res - Express response object used to send the response.
 * @returns {Promise<void>} Sends a JSON response with the panel data or an error message.
 */
const exploreFileData = async (req, res) => {
  try {
      const { csvId } = req.params;
      const {id} = req.user
      const fileRecord = await CSVFilesSchema.findOne({
          where: {
              csv_id: csvId,
          }
      });

      if (!fileRecord) {
          return generateResponse(res, 404, 'File not found.')
      }

      const key = fileRecord?.file_path;
      const queryParams = req.query;
      const requestPayload = req.body;

      // Check if the result exists in cache.
      const cacheKey = generateCacheKey(
          { 
              id,
              key,
              queryParams,
              requestPayload
          },
          `get_panel_data_api_`
      );

      const cachedResult = await getCache(cacheKey);

      // If the result is in cache, return it
      if (cachedResult) {
	      console.log("Returning cached panel result !!");
          return generateResponse(res, 200, "Panel data fetched successfully", cachedResult);
      }

      // Check if the file exists in cache.
      const fileCacheKey = generateCacheKey(
          {
              id,
              key,
          },
          `get_s3_raw_api_`
      );

      let results = [];
      const cachedFileResult = await getCache(fileCacheKey);

      // If the file is not in cache, fetch it
      if (!cachedFileResult || !Array.isArray(cachedFileResult.results)) {
          console.log("Fetching file from S3 !!");
          const bucketName = await gets3BucketName(fileRecord.tenant_id);
          const params = { Bucket: bucketName, Key: key };
          const s3Client = await getS3ClientForTenant(fileRecord.tenant_id);

          // Get file from S3
          const { Body } = await s3Client.send(new GetObjectCommand(params));
          let hasDateTimeColumn = false;
    
          await new Promise((resolve, reject) => {
              Body.pipe(csv())
                  .on("data", (data) => {
                      results.push(data);
                      // Check if DateTime column exists in the first row
                      if (results.length === 1) {
                          hasDateTimeColumn = 'DateTime' in data;
                      }
                  })
                  .on("end", () => {
                      // Only sort if DateTime column exists
                      if (hasDateTimeColumn) {
                          results.sort((a, b) => {
                              const dateA = new Date(a.DateTime);
                              const dateB = new Date(b.DateTime);
                              return dateA - dateB; // ascending order
                          });
                          resolve();
                      } else {
                          // If no DateTime column, keep original order
                          resolve();
                      }
                  })
                  .on("error", (error) => {
                      console.error("❌ CSV Parsing Error:", error);
                      reject(error);
                  });
          });

          // Send response with appropriate message
          const message = hasDateTimeColumn
              ? "File fetched and sorted by DateTime successfully"
              : "File fetched successfully";

          // Save the result in cache
          await setCache(fileCacheKey, { message, results });
      } else {
          // If the file is in cache, extract the message and results
          results = Array.isArray(cachedFileResult.results) ? cachedFileResult.results : [];
      }

      // Make sure results is always an array
      if (!Array.isArray(results)) {
        results = [];
      }

      // Filter results before transforming.
      let filteredResults = filterPanelData(results, requestPayload);
      
      if (Array.isArray(filteredResults) && Array.isArray(filteredResults[0])) {
        filteredResults = filteredResults.slice(1); 
      }

      // Save the filtered results in cache
      // await setCache(cacheKey, filteredResults);

      // Make result plot ready.
      const plotResults = transformPanelData(filteredResults, queryParams?.panelType, requestPayload);

      // Save the result in cache
      await setCache(cacheKey, plotResults);

      return generateResponse(res, 200, "Panel data fetched successfully", plotResults);

  } catch (error) {
      console.error('error :', error);
      return generateResponse(res, 500, 'Failed to retrieve batch data file.')
  }
}

const explorePlcData = async (req, res) => {
  try {
      const {id, tenant_id} = req.user
      const queryParams = req.query;
      const { start_datetime, end_datetime, batch_ids, system, filters } = req.body;

      // Validate required parameters
      if (!start_datetime || !end_datetime || !batch_ids) {
          return generateResponse(res, 400, 'Missing required parameters: start_datetime, end_datetime, batch_ids');
      }

      // Fetch tenant details to get AWS credentials
      const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
      
      if (!tenantData) {
          return generateResponse(res, 404, 'Tenant not found');
      }

      const awsCredentials = tenantData.dataValues.aws_credentials;
      
      if (!awsCredentials || !awsCredentials.AWS_BUCKET_NAME || !awsCredentials.AWS_CONSOLIDATED_PLC) {
          return generateResponse(res, 400, 'AWS credentials not properly configured for tenant');
      }

      // Check if the result exists in cache.
      const cacheKey = generateCacheKey(
          { 
              id,
              queryParams,
              start_datetime,
              end_datetime,
              batch_ids,
              tenant_id,
              filters
          },
          `get_plc_panel_data_api_`
      );

      const cachedResult = await getCache(cacheKey);

      // If the result is in cache, return it
      if (cachedResult) {
	      console.log("Returning cached plc panel result !!");
          return generateResponse(res, 200, "PLC Panel data fetched successfully", cachedResult);
      }

      // Prepare enhanced payload with tenant AWS credentials
      const requestPayload = {
          ...req.body,
          bucket_name: awsCredentials.AWS_BUCKET_NAME,
          path: awsCredentials.AWS_CONSOLIDATED_PLC,
          system: system || ""
      };

      // Get PLC panel data from API.
      const plotResults = await axios.post(
          `${process.env.PLC_EXP_API}/plc_filtered_data`,
          requestPayload
      );

      // Make result plot ready.
      // const plotResults = transformPlcPanelData(response, queryParams);

      // Save the result in cache
      //Uncomment this and check for its working disabled for demo
      // await setCache(cacheKey, plotResults);
      

      return generateResponse(res, 200, "Plc Panel data fetched successfully", plotResults.data);
      
  } catch (error) {
      console.error('error :', error);
      return generateResponse(res, 500, 'Failed to retrieve plc panel data.')
  }
}

const getBatchMetaData = async (req, res) => {
  try {
      const { tenant_id } = req.user;
      const {
        start_datetime,
        end_datetime,
        system,
        filters,
        batch_feature
      } = req.body;

      // Validate required parameters
      if (!start_datetime || !end_datetime) {
          return generateResponse(res, 400, 'Missing required parameter: start_datetime, end_datetime');
      }

      // Fetch tenant details to get AWS credentials
      const tenantData = await Tenants.findOne({ where: { id: tenant_id } });

      if (!tenantData) {
          return generateResponse(res, 404, 'Tenant not found');
      }

      const awsCredentials = tenantData.dataValues.aws_credentials;

      if (!awsCredentials || !awsCredentials.AWS_BUCKET_NAME || !awsCredentials.AWS_CONSOLIDATED_PLC) {
          return generateResponse(res, 400, 'AWS credentials not properly configured for tenant');
      }

      // Check if the result exists in cache.
      const cacheKey = generateCacheKey(
          {
              start_datetime,
              end_datetime,
              filters
          },
          `get_batch_meta_data_api_`
      );

      const cachedResult = await getCache(cacheKey);

      // If the result is in cache, return it
      // if (cachedResult) {
      //   console.log("Returning cached batch meta data !!");
      //     return generateResponse(res, 200, "Batch meta data fetched successfully", cachedResult);
      // }

      // Prepare enhanced payload with tenant AWS credentials and new fields
      const requestPayload = {
          start_datetime,
          end_datetime,
          bucket: awsCredentials.AWS_BUCKET_NAME,
          path: awsCredentials.AWS_CONSOLIDATED_PLC,
          system: system || "",
          batch_feature: batch_feature, 
          x_feature: "",
          y_feature: "",
          ...(filters ? { filters } : {})
      };

      // Get batch metadata from API.
      const response = await axios.post(
          `${process.env.PLC_EXP_API}/fetch_batch_meta_from_s3`,
          requestPayload
      );

      // Enhance response with quality data from system_meta table
      let enhancedResponseData = { ...response.data };

      try {
          // Parse batch_ids from the response metadata
          if (response.data?.metadata?.batch_ids) {
              let batchIds = [];

              try {
                  // Handle stringified array format like "['2505010101' '2505010801' '2505011501' ...]"
                  const batchIdsString = response.data.metadata.batch_ids;

                  if (typeof batchIdsString === 'string') {
                      // Remove brackets and split by spaces, then clean up quotes
                      const cleanedString = batchIdsString.replace(/[\[\]']/g, '');
                      batchIds = cleanedString.split(/\s+/).filter(id => id.trim() !== '');
                  } else if (Array.isArray(batchIdsString)) {
                      batchIds = batchIdsString;
                  }

                  if (batchIds.length > 0) {
                      // Get system_id from system name if provided
                      let systemId = null;
                      if (system) {
                          const systemRecord = await Systems.findOne({
                              where: { name: system },
                              attributes: ['id'],
                              raw: true
                          });
                          systemId = systemRecord?.id;
                      }

                      // Build where clause for SystemMeta query
                      const whereClause = {
                          batch_id: {
                              [Op.in]: batchIds
                          }
                      };

                      // Add system_id filter if system was found
                      if (systemId) {
                          whereClause.system_id = systemId;
                      }

                      // Query system_meta table for quality data and other features
                      const qualityData = await SystemMeta.findAll({
                          where: whereClause,
                          attributes: ['batch_id', 'quality', 'other_info'],
                          raw: true
                      });

                      // Add quality data and other features to the response
                      if (qualityData.length > 0) {
                          enhancedResponseData.quality_data = qualityData.reduce((acc, item) => {
                              if (item.batch_id && item.quality) {
                                  try {
                                      acc[item.batch_id] = JSON.parse(item.quality);
                                  } catch (e) {
                                      acc[item.batch_id] = item.quality; // fallback if not valid JSON
                                  }
                              }
                              return acc;
                          }, {});

                          enhancedResponseData.other_info_data = qualityData.reduce((acc, item) => {
                              if (item.batch_id && item.other_info) {
                                  try {
                                      acc[item.batch_id] = JSON.parse(item.other_info);
                                  } catch (e) {
                                      acc[item.batch_id] = item.other_info;
                                }
                            }
                              return acc;
                          }, {});
                      }
                  }
              } catch (parseError) {
                  console.warn('Failed to parse batch_ids or fetch quality data:', parseError);
                  // Continue without quality data - don't fail the entire request
              }
          }
      } catch (qualityError) {
          console.log('Error fetching quality data from system_meta:', qualityError);
          // Continue without quality data - don't fail the entire request
      }

      // Save the enhanced result in cache
      await setCache(cacheKey, enhancedResponseData);

      return generateResponse(res, 200, "Batch meta data fetched successfully", enhancedResponseData);

  } catch (error) {
      console.error('error :', error);
      if (error.response) {
          // Handle FastAPI validation error (400)
          return generateResponse(
              res,
              error.response.status,
              error.response.data?.message || "Failed to retrieve batch meta data from PLC API.",
              error.response.data
          );
      }
      return generateResponse(res, 500, 'Failed to retrieve batch meta data.')
  }
}


const getPhaseIdentification = async (req, res) => {
  try {
      const { id, tenant_id } = req.user;
      const {
        start_datetime,
        end_datetime,
        batch_ids,
        system,
        x_axis,
        y_axis,
        phase_identifier,
        batch_identifier,
        phases,
        filters
      } = req.body;

      // Validate required fields
      if (!start_datetime || !end_datetime || !batch_ids) {
          return generateResponse(res, 400, 'Missing required fields: start_datetime, end_datetime, batch_id');
      }

      const tenantData = await Tenants.findOne({ where: { id: tenant_id } });
      
      if (!tenantData) {
          return generateResponse(res, 404, 'Tenant not found');
      }

      const awsCredentials = tenantData.dataValues.aws_credentials;

      // Check if the result exists in cache.
      const cacheKey = generateCacheKey(
          {
            start_datetime,
            end_datetime,
            phases,
            batch_ids,
            y_axis,
            filters
          },
          `get_phase_identification_api_`
      );

      const cachedResult = await getCache(cacheKey);

      // If the result is in cache, return it
      if (cachedResult) {
          return generateResponse(res, 200, "Phase identification data fetched successfully", cachedResult);
      }

      const requestPayload = {
          start_datetime,
          end_datetime,
          batch_ids,
          bucket_name: awsCredentials.AWS_BUCKET_NAME,
          path: awsCredentials.AWS_CONSOLIDATED_PLC,
          system,
          x_axis,
          y_axis,
          phase_identifier,
          batch_identifier,
          selected_phases: phases,
          filters: filters || {}
      };

      // Get phase identification data from external API.
      const response = await axios.post(
        `${process.env.PLC_EXP_API}/plc_phase_identification`,
        requestPayload
      );

      // Save the result in cache
      await setCache(cacheKey, response.data);

      return generateResponse(res, 200, "Phase identification data fetched successfully", response.data);

  } catch (error) {
      console.error('Phase Identification API Error:', error);
      return generateResponse(res, 500, 'Failed to retrieve phase identification data.')
  }
}

export {
  uploadFile,
  getFile,
  getFileList,
  getFileColumns,
  removeFile,
  mapCsvFields,
  csvMappingHistory,
  updateFile,
  createClusterAllRunCSVFile,
  getReportData,
  uploadJsonFile,
  getJsonFileData,
  updateJsonFile,
  deleteJsonFile,
  exploreFileData,
  explorePlcData,
  getBatchMetaData,
  getPhaseIdentification
};
